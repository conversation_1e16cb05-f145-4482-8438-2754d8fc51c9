import { logger } from "firebase-functions";
import { isString } from "lodash";

export const buildMessage = (message: any, fields?: Record<string, any>) => {
  return {
    message: isString(message) ? message : JSON.stringify(message),
    json_fields: fields,
  };
};

/**
 * loggerUtils
 */
const loggerUtils = {
  /**
   * logger info function
   * @param content any
   * @param fields Record<string, string>
   */
  info(content: any, fields?: Record<string, any>): void {
    return logger.info(buildMessage(content, fields));
  },
  /**
   * logger error function
   * @param content any
   * @param fields Record<string, string>
   */
  error(content: any, fields: Record<string, any>, error: Error): void {
    return logger.error(buildMessage(content, { ...fields }), error);
  },
  /**
   * logger warn function
   * @param content any
   * @param fields Record<string, string>
   */
  warn(content: any, fields?: Record<string, any>): void {
    return logger.warn(buildMessage(content, fields));
  },
  /**
   * logger debug function
   * @param content any
   * @param fields Record<string, string>
   */
  debug(content: any, fields?: Record<string, any>): void {
    return logger.debug(buildMessage(content, fields));
  },
};

export default loggerUtils;
