import { Injectable } from "@nestjs/common";
import { FindOptionsWhere, In, Like } from "typeorm";

import FleetOrderEntity from "@nest/modules/database/entities/fleetOrder.entity";
import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";

import { AdminFleetOrderQueryDto } from "./dto/adminFleetOrder.dto";

@Injectable()
export class AdminFleetOrderService {
  constructor(private readonly fleetOrderRepository: FleetOrderRepository) {}

  async getFleetOrders(query: AdminFleetOrderQueryDto) {
    const offset = (query.page - 1) * query.limit || 0;
    const where: FindOptionsWhere<FleetOrderEntity> = {};

    if (query.txId) {
      where.txId = query.txId;
    }

    if (query.tripTxId) {
      where.tripTxId = query.tripTxId;
    }

    if (query.statuses) {
      where.status = In(query.statuses);
    }

    if (query.merchantPhone) {
      where.tx = { merchant: { phoneNumber: Like(`%${query.merchantPhone}%`) } };
    }

    if (query.licensePlate) {
      where.tx = { metadata: { licensePlate: Like(`%${query.licensePlate}%`) } };
    }

    if (query.userPhone) {
      where.user = { phoneNumber: Like(`%${query.userPhone}%`) };
    }

    const [fleetOrders, total] = await this.fleetOrderRepository.findAndCount({
      where,
      skip: offset,
      take: query.limit,
      order: { createdAt: "DESC" },
      relations: ["user", "tx", "tx.merchant"],
    });

    return {
      offset,
      limit: query.limit,
      total,
      fleetOrders,
    };
  }

  async getFleetOrder(fleetOrderId: string) {
    return this.fleetOrderRepository.findOne({
      where: { id: fleetOrderId },
      relations: ["user", "tx", "fleetOrderTimelineList", "tripTx"],
    });
  }
}
