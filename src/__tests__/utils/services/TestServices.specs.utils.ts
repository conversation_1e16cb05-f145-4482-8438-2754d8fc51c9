import { AsyncLocalStorage } from "async_hooks";

import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { Cache } from "cache-manager";
import { Firestore } from "firebase-admin/firestore";
import { ClsService } from "nestjs-cls";

import { CloudTaskClientService } from "@nest/modules/cloud-task-client/cloud-task-client.service";
import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";
import { FleetOrderTimelineRepository } from "@nest/modules/database/repositories/fleetOrderTimeline.repository";
import { FleetPartnerRepository } from "@nest/modules/database/repositories/fleetPartner.repository";
import { FleetQuoteRepository } from "@nest/modules/database/repositories/fleetQuote.repository";
import { ReportJobRepository } from "@nest/modules/database/repositories/reportJob.repository";
import { WebhookRepository } from "@nest/modules/database/repositories/webhook.repository";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { GeospatialService } from "@nest/modules/location/geospatial/geospatial.service";
import { CancelFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/CancelFleetOrderDelegatee";
import { CreateFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/CreateFleetOrderDelegatee";
import { QuoteFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/QuoteFleetOrderDelegatee";
import { SyncabCancelFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/SyncabCancelFleetOrderDelegatee";
import { SyncabCreateFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/SyncabCreateFleetOrderDelegatee";
import { SyncabQuoteFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/SyncabQuoteFleetOrderDelegatee";
import { KrakenApi } from "@nest/modules/payment/modules/paymentInstrument/modules/kraken/kraken.api";
import { ReportJobService } from "@nest/modules/reportJob/reportJob.service";
import { SyncabApiService } from "@nest/modules/syncabApi/syncabApi.service";
import { WebhookService } from "@nest/modules/webhook/webhook.service";

import MockAppDatabaseService from "./FakeAppDatabaseService.specs.utils";
import FakeCache from "./FakeCache.specs.utils";
import FakeCampaignService from "./FakeCampaignService.spec.utils";
import { newFakeCancelFleetOrderDelegatee } from "./FakeCancelFleetOrderDelegatee.specs.utils";
import FakeConfigService from "./FakeConfigService.specs.utils";
import { FakeEmailService } from "./FakeEmailService.specs.utils";
import FakeHttpService from "./FakeHttpService.specs.utils";
import FakePubSubService from "./FakePubSubService.specs.utils";
import FakeRepository from "./FakeRepository.specs.utils";
import { FakeRSAEncryptionService } from "./FakeRsaEncryptionService.spec.utils";
import { FakeSecretsService } from "./FakeSecretsService.specs.utils";
import FakeWebhookService from "./FakeWebhookService.specs.utils";
import { AdminNotificationController } from "../../../nestJs/modules/admin/adminNotification/admin-notification.controller";
import { AdminNotificationService } from "../../../nestJs/modules/admin/adminNotification/admin-notification.service";
import { AdminPaymentInstrumentController } from "../../../nestJs/modules/admin/adminPaymentInstrument/adminPaymentInstrument.controller";
import { AdminTransactionController } from "../../../nestJs/modules/admin/adminTransaction/adminTransaction.controller";
import { AdminTransactionService } from "../../../nestJs/modules/admin/adminTransaction/adminTransaction.service";
import { AdminUserController } from "../../../nestJs/modules/admin/adminUser/adminUser.controller";
import { AppDatabaseService } from "../../../nestJs/modules/appDatabase/appDatabase.service";
import { AuthController } from "../../../nestJs/modules/auth/auth.controller";
import { AuthService } from "../../../nestJs/modules/auth/auth.service";
import { BankService } from "../../../nestJs/modules/bank/bank.service";
import { BankFactoryService } from "../../../nestJs/modules/bank/bankFactory/bankFactory.service";
import { DbsService } from "../../../nestJs/modules/bank/bankFactory/modules/dbs/dbs.service";
import { CampaignService } from "../../../nestJs/modules/campaign/campaign.service";
import { TxAppRepository } from "../../../nestJs/modules/database/repositories/app.repository";
import { CampaignRepository } from "../../../nestJs/modules/database/repositories/campaign.repository";
import { DiscountRepository } from "../../../nestJs/modules/database/repositories/discount.repository";
import { MerchantRepository } from "../../../nestJs/modules/database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "../../../nestJs/modules/database/repositories/merchantNotificationToken.repository";
import { MessageRepository } from "../../../nestJs/modules/database/repositories/message.repository";
import { PaymentInstrumentRepository } from "../../../nestJs/modules/database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../../../nestJs/modules/database/repositories/paymentTx.repository";
import { PayoutRepository } from "../../../nestJs/modules/database/repositories/payout.repository";
import { ProfileAuditRepository } from "../../../nestJs/modules/database/repositories/profileAudit.repository";
import { TxRepository } from "../../../nestJs/modules/database/repositories/tx.repository";
import { TxEventRepository } from "../../../nestJs/modules/database/repositories/txEvent.repository";
import { TxTagRepository } from "../../../nestJs/modules/database/repositories/txTag.repository";
import { UserRepository } from "../../../nestJs/modules/database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../nestJs/modules/database/repositories/userNotificationToken.repository";
import { EmailService } from "../../../nestJs/modules/email/email.service";
import { RSAEncryptionService } from "../../../nestJs/modules/encryption/rsaEncryption.service";
import { FcmService } from "../../../nestJs/modules/fcm/fcm.service";
import { FleetService } from "../../../nestJs/modules/fleet/fleet.service";
import { HailingService } from "../../../nestJs/modules/hailing/hailing.service";
import { LocationService } from "../../../nestJs/modules/location/location.service";
import { MeHailingController } from "../../../nestJs/modules/me/modules/meHailing/meHailing.controller";
import { MeHailingService } from "../../../nestJs/modules/me/modules/meHailing/meHailing.service";
import { MeInitializeService } from "../../../nestJs/modules/me/modules/meInitialize/meInitialize.service";
import { MeLocationController } from "../../../nestJs/modules/me/modules/meLocation/meLocation.controller";
import { MeLocationService } from "../../../nestJs/modules/me/modules/meLocation/meLocation.service";
import { MeLogoutService } from "../../../nestJs/modules/me/modules/meLogout/meLogout.service";
import { MeNotificationTokenService } from "../../../nestJs/modules/me/modules/meNotificationToken/meNotificationToken.service";
import { MePaymentInstrumentService } from "../../../nestJs/modules/me/modules/mePaymentInstrument/mePaymentInstrument.service";
import { MePinService } from "../../../nestJs/modules/me/modules/mePin/mePin.service";
import { MeQrCodeService } from "../../../nestJs/modules/me/modules/meQrCode/meQrCode.service";
import { MeTransactionController } from "../../../nestJs/modules/me/modules/meTransaction/meTransaction.controller";
import { MeTransactionService } from "../../../nestJs/modules/me/modules/meTransaction/meTransaction.service";
import { MerchantDriverController } from "../../../nestJs/modules/merchant/merchantDriver/merchantDriver.controller";
import { DriverService } from "../../../nestJs/modules/merchant/merchantDriver/merchantDriver.service";
import { WhatsappServices } from "../../../nestJs/modules/message/messageFactory/modules/whatsapp/whatsapp.service";
import { MessageTeamsService } from "../../../nestJs/modules/messageTeams/messageTeams.service";
import { GlobalPaymentService } from "../../../nestJs/modules/payment/modules/paymentInstrument/modules/globalPayment/globalPayment.service";
import { KrakenService } from "../../../nestJs/modules/payment/modules/paymentInstrument/modules/kraken/kraken.service";
import { PaymentInstrumentService } from "../../../nestJs/modules/payment/modules/paymentInstrument/paymentInstrument.service";
import { PaymentService } from "../../../nestJs/modules/payment/payment.service";
import { GlobalPaymentPaymentService } from "../../../nestJs/modules/payment/paymentFactory/modules/globalPayment/globalPaymentPayment.service";
import { ManualService } from "../../../nestJs/modules/payment/paymentFactory/modules/manual/manual.service";
import { SoepayService } from "../../../nestJs/modules/payment/paymentFactory/modules/soepay/soepay.service";
import { PaymentFactoryService } from "../../../nestJs/modules/payment/paymentFactory/paymentFactory.service";
import { PubSubService } from "../../../nestJs/modules/pubsub/pubsub.service";
import { DashFactoryService } from "../../../nestJs/modules/qrCode/modules/Dash/dashFactory.service";
import { QrCodeFactoryService } from "../../../nestJs/modules/qrCode/qrCodeFactory.service";
import { SecretsService } from "../../../nestJs/modules/secrets/secrets.service";
import { StorageService } from "../../../nestJs/modules/storage/storage.service";
import { TransactionEventService } from "../../../nestJs/modules/transaction/modules/transactionEvent.service";
import { TransactionEventHailingRequestService } from "../../../nestJs/modules/transaction/modules/transactionEventHailingRequest.service";
import { TransactionService } from "../../../nestJs/modules/transaction/transaction.service";
import { TransactionHailingService } from "../../../nestJs/modules/transaction/transactionFactory/modules/transactionHailing/transactionHailing.service";
import { TripService } from "../../../nestJs/modules/transaction/transactionFactory/modules/trip/trip.service";
import { TransactionFactoryService } from "../../../nestJs/modules/transaction/transactionFactory/transactionFactory.service";
import { UserService } from "../../../nestJs/modules/user/user.service";
import ClsContextStorageService from "../../../nestJs/modules/utils/context/clsContextStorage.service";
import LoggerServiceAdapter from "../../../nestJs/modules/utils/logger/logger.service";
import { UtilsService } from "../../../nestJs/modules/utils/utils.service";

export const newTestAppDatabaseService = () => {
  return new AppDatabaseService(new Firestore(), new UtilsService(), newTestLoggerServiceAdapter());
};

export const newTestSoepayService = () => {
  return new SoepayService(
    newTestConfigService(),
    new HttpService(),
    new FakeRepository() as unknown as PaymentTxRepository,
    newTestLoggerServiceAdapter(),
  );
};

export const newTestConfigService = () => {
  return new FakeConfigService() as unknown as ConfigService;
};

export const newTestCache = () => {
  return new FakeCache() as unknown as Cache;
};

export const newTestManualService = () => {
  return new ManualService();
};

export const newTestPaymentFactoryService = () => {
  return new PaymentFactoryService(
    newTestSoepayService(),
    newTestManualService(),
    newTestGlobalPaymentPaymentService(),
    newTestKrakenService(),
  );
};

export const newTestGlobalPaymentPaymentService = () => {
  return new GlobalPaymentPaymentService(
    newTestGlobalPaymentService(),
    new FakeRepository() as unknown as PaymentTxRepository,
  );
};

export const newTestPaymentService = () => {
  return new PaymentService(
    newTestPaymentFactoryService(),
    new FakeRepository() as unknown as PaymentTxRepository,
    new FakeRepository() as unknown as TxTagRepository,
    new FakeRepository() as unknown as PaymentInstrumentRepository,
    new FakeRepository() as unknown as TxRepository,
    newTestLoggerServiceAdapter(),
    newTestAppDatabaseService(),
  );
};

export const newTestDbsService = () => {
  return new DbsService(new FakeRepository() as unknown as TxRepository, newTestAppDatabaseService());
};

export const newTestBankFactoryService = () => {
  return new BankFactoryService(newTestDbsService());
};

export const newTestBankService = () => {
  return new BankService(newTestBankFactoryService());
};

export const newTestStorageService = () => {
  return new StorageService(newTestConfigService());
};

export const newTestPubSubService = () => {
  return new PubSubService(
    newTestConfigService(),
    newTestClsContextStorageService(),
    newTestLoggerServiceAdapter(),
    newTestCache(),
  );
};

export const newTestTransactionFactoryService = () => {
  return new TransactionFactoryService(newTestTripService(), newTestTransactionHailingService());
};

export const newTestTransactionHailingService = () => {
  return new TransactionHailingService(
    newTestAppDatabaseService(),
    new FakeCampaignService() as unknown as CampaignService,
    newTestLoggerServiceAdapter(),
  );
};

export const newTestTripService = () => {
  return new TripService(
    newTestPubSubService(),
    newTestPaymentService(),
    newTestConfigService(),
    new UtilsService(),
    newTestAppDatabaseService(),
    new FakeRepository() as unknown as TxRepository,
    new FakeRepository() as unknown as TxAppRepository,
    new FakeRepository() as unknown as DiscountRepository,
    newTestLoggerServiceAdapter(),
    newTestCache(),
    newTestFcmService(),
    new FakeRepository() as unknown as UserRepository,
    new FakeRepository() as unknown as UserNotificationTokenRepository,
    newTestLocationService(),
    newTestCampaignService(),
  );
};

export const newTestClsContextStorageService = () => {
  return new ClsContextStorageService(new ClsService(new AsyncLocalStorage()));
};

export const newTestLoggerServiceAdapter = () => {
  return new LoggerServiceAdapter(newTestClsContextStorageService());
};

export const newTestTransactionService = () => {
  return new TransactionService(
    newTestAppDatabaseService(),
    new FakeRepository() as unknown as MerchantRepository,
    newTestPaymentService(),
    new FakeRepository() as unknown as TxRepository,
    new FakeRepository() as unknown as TxTagRepository,
    new FakeRepository() as unknown as PayoutRepository,
    newTestBankService(),
    newTestStorageService(),
    newTestPubSubService(),
    newTestTransactionFactoryService(),
    new UtilsService(),
    newTestLoggerServiceAdapter(),
    new FakeRepository() as unknown as PaymentInstrumentRepository,
    new FakeRepository() as unknown as PaymentTxRepository,
    newTestMessageTeamsService(),
    newTestTransactionEventService(),
    new FakeRepository() as unknown as UserRepository,
    newTestSecretService(),
    newTestEmailService(),
    new FakeRepository() as unknown as DiscountRepository,
    new FakeRepository() as unknown as TxEventRepository,
  );
};

export const newTestTransactionEventService = () => {
  return new TransactionEventService(
    new FakeRepository() as unknown as TxRepository,
    newTestTransactionEventHailingRequest(),
  );
};

export const newTestSyncabCancelFleetOrderDelegatee = () => {
  return new SyncabCancelFleetOrderDelegatee(newTestSyncabApiService());
};

export const newTestHailingApiService = () => {
  return new HailingApiService(newTestHttpService(), newTestConfigService(), newTestLoggerServiceAdapter());
};

export const newTestCancelFleetOrderDelegatee = () => {
  return new CancelFleetOrderDelegatee(
    new FakeRepository() as unknown as FleetOrderRepository,
    newTestSyncabCancelFleetOrderDelegatee(),
    newTestHailingApiService(),
    newTestLoggerServiceAdapter(),
  );
};

export const newTestTransactionEventHailingRequest = () => {
  return new TransactionEventHailingRequestService(
    new FakeRepository() as unknown as MerchantRepository,
    newTestPaymentService(),
    new FakeRepository() as unknown as TxRepository,
    new FakeRepository() as unknown as PaymentTxRepository,
    newTestPubSubService(),
    newTestTransactionFactoryService(),
    newTestLoggerServiceAdapter(),
    new MockAppDatabaseService() as unknown as AppDatabaseService,
    newFakeCancelFleetOrderDelegatee() as unknown as CancelFleetOrderDelegatee,
  );
};

export const newTestMessageTeamsService = () => {
  return new MessageTeamsService(newTestLoggerServiceAdapter(), newTestConfigService());
};

export const newTestDashFactoryService = () => {
  return new DashFactoryService(newTestTransactionService());
};

export const newTestQrCodeFactoryService = () => {
  return new QrCodeFactoryService(newTestConfigService(), newTestDashFactoryService());
};

export const newTestMeQrCodeService = () => {
  return new MeQrCodeService(newTestQrCodeFactoryService());
};

export const newTestMePinService = () => {
  return new MePinService(
    new FakeRepository() as unknown as UserRepository,
    new FakeRepository() as unknown as ProfileAuditRepository,
    new FakePubSubService() as unknown as PubSubService,
  );
};

export const newTestCampaignService = () => {
  return new CampaignService(
    new FakeRepository() as unknown as CampaignRepository,
    new FakeRepository() as unknown as UserRepository,
    new FakeRepository() as unknown as DiscountRepository,
    new UtilsService(),
    newTestLoggerServiceAdapter(),
    new FakeRepository() as unknown as UserService,
  );
};

export const newTestUserService = () => {
  return new UserService(
    new FakeRepository() as unknown as UserRepository,
    new UtilsService(),
    newTestAppDatabaseService(),
    newTestLoggerServiceAdapter(),
    new FakeRepository() as unknown as ProfileAuditRepository,
  );
};

export const newTestAuthService = () => {
  return new AuthService(
    newTestConfigService(),
    new MockAppDatabaseService() as unknown as AppDatabaseService,
    newTestLoggerServiceAdapter(),
    newTestEmailService(),
  );
};

export const newTestEmailService = () => {
  return new FakeEmailService() as unknown as EmailService;
};

export const newTestSecretService = () => {
  return new FakeSecretsService() as unknown as SecretsService;
};

export const newRSAEncryptionService = () => {
  return new FakeRSAEncryptionService() as unknown as RSAEncryptionService;
};

export const newTestAuthController = () => {
  return new AuthController(newTestAuthService(), newTestLoggerServiceAdapter(), newRSAEncryptionService());
};

export const newTestAdminUserController = () => {
  return new AdminUserController(newTestUserService());
};

export const newTestAdminPaymentInstrumentController = () => {
  return new AdminPaymentInstrumentController(newTestPaymentInstrumentService());
};

export const newTestAdminTransactionService = () => {
  return new AdminTransactionService(
    newTestTransactionService(),
    newTestDriverService(),
    new FakeRepository() as unknown as MerchantRepository,
    newTestLoggerServiceAdapter(),
  );
};

export const newTestAdminTransactionController = () => {
  return new AdminTransactionController(newTestTransactionService(), newTestAdminTransactionService());
};

export const newTestHttpService = () => {
  return new FakeHttpService() as unknown as HttpService;
};

export const newTestWhatsappService = () => {
  return new WhatsappServices(
    newTestConfigService(),
    newTestHttpService(),
    new MessageRepository(
      new FakeRepository() as unknown as MessageRepository,
      new UserRepository(new FakeRepository() as unknown as UserRepository),
    ),
    newTestPubSubService(),
    newTestAppDatabaseService(),
    newTestLoggerServiceAdapter(),
  );
};

export const newTestGlobalPaymentService = () => {
  return new GlobalPaymentService(
    newTestConfigService(),
    new FakeRepository() as unknown as PaymentTxRepository,
    newTestLoggerServiceAdapter(),
    new FakeRepository() as unknown as PaymentInstrumentRepository,
    newTestKrakenService(),
    newTestAppDatabaseService(),
  );
};

export const newTestPaymentInstrumentService = () => {
  return new PaymentInstrumentService(
    newTestGlobalPaymentService(),
    new FakeRepository() as unknown as PaymentInstrumentRepository,
    new FakeRepository() as unknown as UserRepository,
    new FakeRepository() as unknown as TxAppRepository,
    new FakeRepository() as unknown as TxRepository,
    new FakeRepository() as unknown as PaymentTxRepository,
    newTestAppDatabaseService(),
    newTestKrakenService(),
    newTestLoggerServiceAdapter(),
    newTestConfigService(),
  );
};

export const newTestKrakenApi = () => {
  return new KrakenApi(newTestConfigService());
};

export const newTestKrakenService = () => {
  return new KrakenService(
    newTestConfigService(),
    new FakeRepository() as unknown as PaymentTxRepository,
    newTestLoggerServiceAdapter(),
    newTestKrakenApi(),
    new FakeRepository() as unknown as PaymentInstrumentRepository,
  );
};

export const newTestMePaymentInstrumentService = () => {
  return new MePaymentInstrumentService(newTestPaymentInstrumentService());
};

export const newTestMeNotificationTokenService = () => {
  return new MeNotificationTokenService(new FakeRepository() as unknown as UserNotificationTokenRepository);
};

export const newTestMeInitializeService = () => {
  return new MeInitializeService(
    newTestMeNotificationTokenService(),
    newTestUserService(),
    new FakeRepository() as unknown as UserRepository,
  );
};

export const newTestDriverService = () => {
  return new DriverService(
    newTestStorageService(),
    new FakeRepository() as unknown as MerchantRepository,
    new FakeRepository() as unknown as TxRepository,
    new FakeRepository() as unknown as MerchantNotificationTokenRepository,
    newTestAppDatabaseService(),
    newTestLoggerServiceAdapter(),
    new UtilsService(),
    newTestHailingService(),
    newTestTripService(),
    newTestFcmService(),
    new FakeCache() as unknown as Cache,
    new FakeWebhookService() as unknown as WebhookService,
    new FakeRepository() as unknown as WebhookRepository,
  );
};

export const newTestDriverController = () => {
  return new MerchantDriverController(
    newTestDriverService(),
    newTestLoggerServiceAdapter(),
    newTestMessageTeamsService(),
    newTestPubSubService(),
  );
};

export const newTestLocationService = () => {
  return new LocationService(newTestConfigService(), newTestGeospatialService(), newTestLoggerServiceAdapter());
};

export const newTestGeospatialService = () => {
  return new GeospatialService(newTestLoggerServiceAdapter(), newTestConfigService(), false);
};

export const newTestMeLocationService = () => {
  return new MeLocationService(newTestLocationService());
};

export const newTestMeLocationController = () => {
  return new MeLocationController(newTestMeLocationService());
};

export const newTestHailingService = () => {
  return new HailingService(
    newTestPaymentService(),
    newTestLocationService(),
    newTestConfigService(),
    newTestCampaignService(),
    new FakeRepository() as unknown as TxRepository,
    new FakeRepository() as unknown as TxAppRepository,
    new FakeRepository() as unknown as UserRepository,
    newTestLoggerServiceAdapter(),
    newTestPubSubService(),
    new UtilsService(),
    newTestAppDatabaseService(),
  );
};

export const newTestFleetService = () => {
  return new FleetService(newTestAppDatabaseService());
};

export const newTestQuoteFleetOrderDelegatee = () => {
  return new QuoteFleetOrderDelegatee(
    newTestLocationService(),
    new FakeRepository() as unknown as FleetQuoteRepository,
    newTestSyncabQuoteFleetOrderDelegatee(),
    new FakeRepository() as unknown as UserRepository,
    newTestLoggerServiceAdapter(),
  );
};

export const newTestSyncabQuoteFleetOrderDelegatee = () => {
  return new SyncabQuoteFleetOrderDelegatee(newTestSyncabApiService(), newTestAppDatabaseService());
};

export const newTestSyncabApiService = () => {
  return new SyncabApiService(newTestHttpService(), newTestLoggerServiceAdapter());
};

export const newTestSyncabCreateFleetOrderDelegatee = () => {
  return new SyncabCreateFleetOrderDelegatee(newTestSyncabApiService());
};

export const newTestCloudTaskClientService = () => {
  return new CloudTaskClientService(newTestConfigService(), newTestLoggerServiceAdapter());
};

export const newTestCreateFleetOrderDelegatee = () => {
  return new CreateFleetOrderDelegatee(
    newTestSyncabCreateFleetOrderDelegatee(),
    new FakeRepository() as unknown as UserRepository,
    new FakeRepository() as unknown as FleetQuoteRepository,
    new FakeRepository() as unknown as FleetOrderRepository,
    new FakeRepository() as unknown as FleetOrderTimelineRepository,
    newTestLoggerServiceAdapter(),
    newTestCloudTaskClientService(),
    new FakeRepository() as unknown as FleetPartnerRepository,
    new FakeRepository() as unknown as TxRepository,
    newTestPaymentService(),
    newTestHailingApiService(),
  );
};

export const newTestMeHailingService = () => {
  return new MeHailingService(
    newTestHailingService(),
    newTestLocationService(),
    newTestFleetService(),
    newTestCreateFleetOrderDelegatee(),
    newTestPubSubService(),
    newTestLoggerServiceAdapter(),
  );
};

export const newTestMeHailingController = () => {
  return new MeHailingController(newTestMeHailingService());
};

export const newTestMeTransactionController = () => {
  return new MeTransactionController(newTestMeTransactionService(), newTestAppDatabaseService());
};

export const newTestMeTransactionService = () => {
  return new MeTransactionService(
    newTestTransactionService(),
    new FakeRepository() as unknown as TxRepository,
    new FakeRepository() as unknown as UserRepository,
    newTestLoggerServiceAdapter(),
  );
};

export const newTestFcmService = () => {
  return new FcmService(
    newTestLoggerServiceAdapter(),
    newTestAppDatabaseService(),
    new FakeRepository() as unknown as UserRepository,
    new FakeRepository() as unknown as UserNotificationTokenRepository,
    new FakeRepository() as unknown as MerchantRepository,
    new FakeRepository() as unknown as MerchantNotificationTokenRepository,
  );
};

export const newTestReportJobService = () => {
  return new ReportJobService(
    new FakeRepository() as unknown as ReportJobRepository,
    newTestPubSubService(),
    newTestLoggerServiceAdapter(),
    newTestConfigService(),
    newTestEmailService(),
    newTestSecretService(),
    newTestMessageTeamsService(),
  );
};

export const newTestMeLogoutService = () => {
  return new MeLogoutService(new FakeRepository() as unknown as UserRepository, newTestMeNotificationTokenService());
};

// Import AdminNotification related classes

// Mock services for AdminNotification tests
export const newTestAdminNotificationService = () => {
  return {
    createNotifications: jest.fn(),
    updateNotifications: jest.fn(),
    deleteNotifications: jest.fn(),
    getNotifications: jest.fn(),
  } as unknown as AdminNotificationService;
};

export const newTestAdminNotificationController = () => {
  return new AdminNotificationController(newTestLoggerServiceAdapter(), newTestAdminNotificationService());
};
