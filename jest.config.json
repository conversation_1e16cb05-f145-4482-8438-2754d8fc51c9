{"bail": 1, "verbose": true, "preset": "ts-jest", "testEnvironment": "node", "transform": {"^.+\\.ts?$": ["@swc/jest"], "^.+\\.[t|j]sx?$": "babel-jest"}, "modulePathIgnorePatterns": [], "testMatch": [], "moduleNameMapper": {"^@tests/(.*)$": "<rootDir>/src/__tests__/$1", "^@migrations/(.*)$": "<rootDir>/src/__migrations__/$1", "^@functions/(.*)$": "<rootDir>/src/functions/$1", "^@static/(.*)$": "<rootDir>/src/static/$1", "^@legacy/(.*)$": "<rootDir>/src/legacy/$1", "^@nest/(.*)$": "<rootDir>/src/nestJs/$1"}, "collectCoverageFrom": ["src/nestJs/**/*.{ts,tsx}", "!src/__tests__/**", "!src/__tests_integration__/**", "!src/__migrations__/**", "!src/**/*.module.{ts,tsx}"]}